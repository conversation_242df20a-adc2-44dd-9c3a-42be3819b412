import { ForumCategoryTitleResolver } from "@/resolver/forum-category-title.resolver"
import { ForumTopicTitleResolver } from "@/resolver/forum-topic-title.resolver"
import { Routes } from '@angular/router'
import { authGuard } from "./guards/auth.guard"
import { CategoryTitleResolver } from './resolver/category-title.resolver'


export const routes: Routes = [
  {
    path: '',
    redirectTo: 'ru',
    pathMatch: 'full'
  },
  {
    path: ':lang',
    loadComponent: () => import('@/components/layout/layout.component').then(m => m.LayoutComponent),
    children: [
      {
        path: '',
        loadComponent: () => import('@/pages/main/main.component').then(m => m.MainComponent),
      },
      {
        path: 'new-main',
        loadComponent: () => import('@/pages/main-v2/main-v2.component').then(m => m.MainV2Component),
      },
      {
        path: 'photo',
        data: { breadcrumb: 'Фотогалерея' },
        children: [
          {
            path: '',
            loadComponent: () => import('@/components/photo/photo.component').then(m => m.PhotoComponent),
          },
          {
            path: ':id',
            loadComponent: () => import('@/components/photo/photo.component').then(m => m.PhotoComponent),
            // resolve: {
            //   resolvedBreadcrumb: PhotoTitleResolver
            // },
            data: { breadcrumb: 'Фото' }
          },
        ]
      },
      {
        path: 'audiogallery/audiolektsii',
        data: { breadcrumb: 'Лекции' },
        children: [
          {
            path: '',
            loadComponent: () => import('@/pages/audio-gallery/audio-gallery.component').then(m => m.AudioGalleryComponent),
          },
          {
            path: ':param',
            loadComponent: () => import('@/pages/lecture/lecture.component').then(m => m.LectureComponent),
          }
        ]
      },
      {
        path: 'audiogallery/videolektsii',
        loadComponent: () => import('@/pages/audio-gallery/audio-gallery.component').then(m => m.AudioGalleryComponent),
        data: {
          video: true,
          breadcrumb: 'Лекции'
        }
      },
      {
        path: 'library',
        data: { breadcrumb: 'library' },
        children: [
          {
            path: '',
            loadComponent: () => import('@/pages/library/library-list/library-list.component').then(m => m.LibraryListComponent),
          },
          {
            path: ':code',
            loadComponent: () => import('@/pages/library/library.component').then(m => m.LibraryComponent),
            // resolve: {
            //   resolvedBreadcrumb: LibraryTitleResolver
            // },
            data: { breadcrumb: 'library' }
          }
        ]
      },
      {
        path: 'profile',
        canActivate: [authGuard],
        data: { breadcrumb: 'Профиль' },
        children: [
          {
            path: '',
            redirectTo: 'favorites',
            pathMatch: 'full'
          },
          {
            path: 'favorites',
            loadComponent: () => import('@/pages/profile/profile.component').then(m => m.ProfileComponent),
            data: { breadcrumb: 'Избранное' },
          },
          {
            path: 'favorites/:subtab',
            loadComponent: () => import('@/pages/profile/profile.component').then(m => m.ProfileComponent),
            data: { breadcrumb: 'Избранное' },
          },
          {
            path: 'my-data',
            loadComponent: () => import('@/pages/profile/profile.component').then(m => m.ProfileComponent),
            data: { breadcrumb: 'Мои данные' },
          },
          {
            path: 'my-data/:subtab',
            loadComponent: () => import('@/pages/profile/profile.component').then(m => m.ProfileComponent),
            data: { breadcrumb: 'Мои данные' },
          },
          {
            path: 'playlists',
            loadComponent: () => import('@/pages/profile/profile.component').then(m => m.ProfileComponent),
            data: { breadcrumb: 'Плейлисты' },
          },
          {
            path: 'subscriptions',
            loadComponent: () => import('@/pages/profile/profile.component').then(m => m.ProfileComponent),
            data: { breadcrumb: 'Подписки' },
          },
        ]
      },

      // {
      //   path: 'profile/playlist/add',
      //   loadComponent: () => import('@/pages/profile/playlist/playlist-add/playlist-add.component').then(m => m.PlaylistAddComponent),
      //   canActivate: [authGuard]
      // },
      {
        path: 'sitemap',
        loadComponent: () => import('./pages/sitemap/sitemap.component').then(m => m.SitemapComponent),
      },
      {
        path: 'mypage/:id',
        loadComponent: () => import('@/pages/mypage/mypage.component').then(m => m.MypageComponent),
      },
      {
        path: 'forum',
        data: { breadcrumb: 'Форум' },
        children: [
          {
            path: '',
            loadComponent: () => import('@/pages/forum/forum.component').then(m => m.ForumComponent),
          },
          {
            path: ':id',
            resolve: {
              resolvedBreadcrumb: ForumCategoryTitleResolver
            },
            loadComponent: () => import('@/pages/forum/forum-category/forum-category.component').then(m => m.ForumCategoryComponent),
          },
          {
            path: 'topic/:id',
            resolve: {
              resolvedBreadcrumb: ForumTopicTitleResolver
            },
            loadComponent: () => import('@/pages/forum/forum-topic/forum-topic.component').then(m => m.ForumTopicComponent),
          },
        ],
      },
      {
        path: 'categories',
        data: { breadcrumb: 'Категории' },
        children: [
          {
            path: '',
            loadComponent: () => import('@/pages/categories/categories.component').then(m => m.CategoriesComponent),
          },
          {
            path: ':id',
            resolve: {
              resolvedBreadcrumb: CategoryTitleResolver
            },
            children: [
              {
                path: '',
                loadComponent: () => import('@/pages/categories/category/category.component').then(m => m.CategoryComponent),
              },
              {
                path: ':page',
                loadComponent: () => import('@/pages/content/content.component').then(m => m.ContentComponent),
              }
            ]
          },
        ]
      },
      {
        path: 'anketa',
        loadComponent: () => import('@/pages/form/form.component').then(m => m.FormComponent),
      },
      {
        path: ':category/:page',
        loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
      },
      {
        path: 'search',
        loadComponent: () => import('./components/search/search.component').then(m => m.SearchComponent),
      },
      {
        path: 'news',
        data: { breadcrumb: 'Новости' },
        loadComponent: () => import('./pages/news/news.components').then(m => m.NewsComponent),
      },
      {
        path: 'audiofiles',
        loadComponent: () => import('@/pages/audiofiles/audiofiles.component').then(m => m.AudiofilesComponent),
      },
      {
        path: 'donation',
        data: { breadcrumb: 'Пожертвование' },
        children: [
          {
            path: '',
            redirectTo: 'bank-transfer',
            pathMatch: 'full'
          },
          {
            path: 'bank-transfer',
            loadComponent: () => import('@/pages/donation/donation.component').then(m => m.DonationComponent),
            data: { breadcrumb: 'Банковский перевод' },
          },
          {
            path: 'online',
            loadComponent: () => import('@/pages/donation/donation.component').then(m => m.DonationComponent),
            data: { breadcrumb: 'Онлайн' },
          },
        ]
      },
      {
        path: 'notifications',
        loadComponent: () => import('@/pages/notifications/notifications.component').then(m => m.NotificationsComponent),
        data: { breadcrumb: 'Уведомления' },
      },
      {
        path: 'ai-chat',
        data: { breadcrumb: 'AI Чат' },
        children: [
          {
            path: '',
            loadComponent: () => import('@/pages/ai-chat/ai-chat.component').then(m => m.AiChatComponent),
          },
          {
            path: ':chatId',
            loadComponent: () => import('@/pages/ai-chat/ai-chat.component').then(m => m.AiChatComponent),
          }
        ]
      }
    ],
  },
  {
    path: ':lang/signin',
    loadComponent: () => import('@/pages/login/login.component').then(m => m.LoginComponent),
  },
  {
    path: ':lang/signup',
    loadComponent: () => import('@/pages/registration/registration.component').then(m => m.RegistrationComponent),
  },
  {
    path: ':lang/forgot',
    loadComponent: () => import('@/pages/forgot/forgot.component').then(m => m.ForgotComponent),
  },
  {
    path: '**',
    loadComponent: () => import('@/pages/page-not-found/page-not-found.component').then(m => m.PageNotFoundComponent) }
];
